/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* CSS Custom Properties for Color Scheme */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --primary-lighter: #dbeafe;
    
    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-dark: #475569;
    --secondary-light: #94a3b8;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Accent Colors */
    --accent-color: #10b981;
    --accent-dark: #059669;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Playfair Display', Georgia, serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    letter-spacing: -0.02em;
}

h2 {
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 600;
    letter-spacing: -0.01em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 1.875rem);
    font-weight: 500;
}

h4 {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--primary-color);
}

p {
    margin-bottom: var(--spacing-md);
    font-size: 1.125rem;
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover, a:focus {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
    color: var(--white);
    padding: var(--spacing-3xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

header .container {
    position: relative;
    z-index: 1;
}

header h1 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    opacity: 0.9;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

/* Navigation Styles */
nav {
    background: var(--white);
    padding: var(--spacing-lg) 0;
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

nav li {
    margin: 0;
}

nav a {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-weight: 500;
    font-size: 1rem;
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    text-decoration: none;
}

nav a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

nav a:hover, nav a:focus {
    color: var(--primary-color);
    background-color: var(--primary-lighter);
    transform: translateY(-2px);
    text-decoration: none;
}

nav a:hover::before, nav a:focus::before {
    width: 80%;
}

/* Main Content */
main {
    padding: var(--spacing-3xl) 0;
}

/* Section Styles */
section {
    background: var(--white);
    margin: var(--spacing-2xl) 0;
    padding: var(--spacing-3xl) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

section:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

section h2 {
    color: var(--gray-900);
    margin-bottom: var(--spacing-xl);
    position: relative;
    padding-bottom: var(--spacing-md);
}

section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-sm);
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.skill-category {
    background: var(--gray-50);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.skill-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
}

.skill-category:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    background: var(--white);
}

.skill-category h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.125rem;
    font-weight: 600;
}

.skill-list {
    list-style: none;
}

.skill-list li {
    padding: var(--spacing-sm) 0;
    position: relative;
    padding-left: var(--spacing-xl);
    font-size: 1rem;
    color: var(--gray-600);
    transition: color var(--transition-fast);
}

.skill-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
    color: var(--accent-color);
    font-weight: 600;
    font-size: 1.125rem;
}

.skill-list li:hover {
    color: var(--gray-800);
}

/* Experience and Project Items */
.experience-item, .project-item {
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.experience-item::before, .project-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
}

.experience-item:hover, .project-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: var(--white);
}

.experience-header, .project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.job-title, .project-title {
    font-size: 1.375rem;
    font-weight: 600;
    color: var(--gray-900);
    font-family: var(--font-display);
}

.company, .project-tech {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 1.125rem;
}

.date {
    color: var(--gray-500);
    font-style: italic;
    font-size: 1rem;
    white-space: nowrap;
}

.experience-item ul, .project-item ul {
    list-style: none;
    margin-top: var(--spacing-md);
}

.experience-item li, .project-item li {
    padding: var(--spacing-sm) 0;
    position: relative;
    padding-left: var(--spacing-xl);
    color: var(--gray-600);
    line-height: 1.6;
}

.experience-item li::before, .project-item li::before {
    content: "→";
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
    color: var(--primary-color);
    font-weight: 600;
}

/* Contact Grid */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.contact-info {
    background: var(--gray-50);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.contact-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.contact-info:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    background: var(--white);
}

.contact-info h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.125rem;
    font-weight: 600;
}

.contact-info a {
    color: var(--gray-700);
    font-weight: 500;
    font-size: 1.125rem;
    transition: color var(--transition-fast);
}

.contact-info a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-info p {
    color: var(--gray-600);
    font-size: 1.125rem;
    margin: 0;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
    text-decoration: none;
}

.btn-secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    text-decoration: none;
}

/* Footer */
footer {
    background: var(--gray-900);
    color: var(--gray-300);
    text-align: center;
    padding: var(--spacing-2xl) 0;
    margin-top: var(--spacing-3xl);
}

footer p {
    margin: 0;
    font-size: 1rem;
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    z-index: 1000;
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
    color: var(--white);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    section {
        padding: var(--spacing-2xl) var(--spacing-lg);
    }

    .skills-grid, .contact-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    header {
        padding: var(--spacing-2xl) 0;
    }

    nav {
        padding: var(--spacing-md) 0;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
    }

    nav a {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }

    main {
        padding: var(--spacing-2xl) 0;
    }

    section {
        margin: var(--spacing-lg) 0;
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .experience-header, .project-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .date {
        white-space: normal;
    }

    .skills-grid, .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .skill-category, .contact-info {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    header {
        padding: var(--spacing-xl) 0;
    }

    section {
        padding: var(--spacing-lg) var(--spacing-sm);
        margin: var(--spacing-md) 0;
    }

    .experience-item, .project-item {
        padding: var(--spacing-lg);
    }

    .skill-category, .contact-info {
        padding: var(--spacing-md);
    }

    p {
        font-size: 1rem;
    }
}

/* Accessibility and Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #0f172a;
        --gray-100: #1e293b;
        --gray-200: #334155;
        --gray-300: #475569;
        --gray-700: #cbd5e1;
        --gray-800: #e2e8f0;
        --gray-900: #f1f5f9;
        --white: #0f172a;
    }

    body {
        background-color: var(--gray-100);
    }

    section {
        background: var(--gray-50);
        border: 1px solid var(--gray-200);
    }

    .skill-category, .contact-info, .experience-item, .project-item {
        background: var(--gray-100);
        border-color: var(--gray-200);
    }

    .skill-category:hover, .contact-info:hover, .experience-item:hover, .project-item:hover {
        background: var(--gray-50);
    }
}

/* Focus Styles for Better Accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    header {
        background: none !important;
        color: black !important;
    }

    nav {
        display: none;
    }

    section {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        page-break-inside: avoid;
    }

    .skip-link {
        display: none;
    }
}
