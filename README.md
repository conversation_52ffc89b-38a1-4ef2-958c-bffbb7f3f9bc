# Monthly Utility Bill Calculator and Notifier

An automated system that extracts monthly utility bills from your email, calculates equal splits among household members, and sends notifications via email and SMS.

## 🎯 Features

- **Email Integration**: Connects to Gmail, Outlook, Yahoo, and other IMAP email providers
- **Automatic Bill Detection**: Finds electricity and gas bills from City of Austin and Texas Gas Service
- **Smart Bill Parsing**: Extracts bill amounts, due dates, and usage data from email content
- **Equal Bill Splitting**: Automatically calculates each person's share
- **Multi-Channel Notifications**: Sends notifications via email and SMS (Twilio)
- **Database Storage**: Keeps track of all bills and payment history
- **CLI Interface**: Easy-to-use command-line interface
- **Configurable**: Supports multiple utility companies and household configurations

## 🚀 Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Setup the application
python main.py setup
```

### 2. Configuration

Edit `config/config.yaml` with your settings:

```yaml
# Email Configuration
email:
  provider: "gmail"
  username: "<EMAIL>"
  password: "your_app_password"  # Use app-specific password

# Household Members
household:
  total_members: 3
  members:
    - name: "Your Name"
      email: "<EMAIL>"
      phone: "+**********"
    - name: "Roommate 1"
      email: "<EMAIL>"
      phone: "+**********"
```

### 3. Usage

```bash
# Process bills from email
python main.py process

# View bill summary
python main.py summary --months 3

# Manually add a bill
python main.py add-bill --type electricity --amount 125.50 --date 2024-01-15
```

## 📧 Email Setup

### Gmail Setup
1. Enable 2-Factor Authentication
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the app password in your configuration

## 🏠 Supported Utility Companies

### Currently Supported
- **City of Austin** (Electricity)
- **Texas Gas Service** (Gas)

## 🔧 CLI Commands

```bash
# Setup and configuration
python main.py setup                    # Initial setup
python main.py process                  # Process new bills
python main.py summary --months 3      # Show bill summary

# Manual operations
python main.py add-bill --type gas --amount 75.25 --date 2024-01-15
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.