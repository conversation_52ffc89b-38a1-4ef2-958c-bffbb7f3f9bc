#!/usr/bin/env python3
"""
Monthly Utility Bill Calculator and Notifier
Main application entry point
"""

import os
import sys
import click
from pathlib import Path
from loguru import logger

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config_manager import ConfigManager
from src.email_handler.email_client import EmailClient
from src.data_extractor.bill_parser import BillParser
from src.database.db_manager import DatabaseManager
from src.calculator.bill_calculator import BillCalculator
from src.notifier.notification_service import NotificationService


class UtilityBillApp:
    """Main application class for the utility bill calculator."""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """Initialize the application with configuration."""
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # Setup logging
        self._setup_logging()
        
        # Initialize components
        self.email_client = EmailClient(self.config['email'])
        self.bill_parser = BillParser(self.config['email']['search_criteria'])
        self.db_manager = DatabaseManager(self.config['database'])
        self.calculator = BillCalculator(self.config['household'])
        self.notifier = NotificationService(self.config['notifications'])
        
        logger.info("Utility Bill Calculator initialized successfully")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_config = self.config.get('logging', {})
        log_level = log_config.get('level', 'INFO')
        log_file = log_config.get('file', 'logs/utility_calculator.log')
        
        # Create logs directory if it doesn't exist
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # Configure logger
        logger.remove()  # Remove default handler
        logger.add(sys.stdout, level=log_level, format="{time} | {level} | {message}")
        logger.add(log_file, level=log_level, format="{time} | {level} | {file}:{line} | {message}")
    
    def process_bills(self):
        """Main method to process utility bills."""
        try:
            logger.info("Starting bill processing...")
            
            # Step 1: Connect to email and fetch bills
            logger.info("Connecting to email...")
            self.email_client.connect()
            
            # Step 2: Search for electricity and gas bills
            electricity_emails = self.email_client.search_bills('electricity')
            gas_emails = self.email_client.search_bills('gas')
            
            logger.info(f"Found {len(electricity_emails)} electricity bills and {len(gas_emails)} gas bills")
            
            # Step 3: Parse bill data
            all_bills = []
            
            for email_data in electricity_emails + gas_emails:
                bill_data = self.bill_parser.parse_bill(email_data)
                if bill_data:
                    all_bills.append(bill_data)
            
            logger.info(f"Successfully parsed {len(all_bills)} bills")
            
            # Step 4: Store bills in database
            for bill in all_bills:
                self.db_manager.save_bill(bill)
            
            # Step 5: Calculate splits for recent bills
            recent_bills = self.db_manager.get_recent_bills()
            if recent_bills:
                splits = self.calculator.calculate_splits(recent_bills)
                
                # Step 6: Send notifications
                if self.config['processing']['auto_notify']:
                    self.notifier.send_bill_notifications(splits)
                    logger.info("Notifications sent successfully")
                
                return splits
            else:
                logger.info("No recent bills found to process")
                return []
                
        except Exception as e:
            logger.error(f"Error processing bills: {str(e)}")
            raise
        finally:
            self.email_client.disconnect()
    
    def get_bill_summary(self, months: int = 3):
        """Get a summary of bills for the last N months."""
        return self.db_manager.get_bill_summary(months)
    
    def manual_add_bill(self, bill_type: str, amount: float, date: str):
        """Manually add a bill to the database."""
        bill_data = {
            'type': bill_type,
            'amount': amount,
            'date': date,
            'source': 'manual'
        }
        self.db_manager.save_bill(bill_data)
        logger.info(f"Manually added {bill_type} bill: ${amount}")


@click.group()
def cli():
    """Monthly Utility Bill Calculator and Notifier CLI."""
    pass


@cli.command()
@click.option('--config', default='config/config.yaml', help='Path to configuration file')
def process(config):
    """Process utility bills from email."""
    app = UtilityBillApp(config)
    splits = app.process_bills()
    
    if splits:
        click.echo("\n=== Bill Splits ===")
        for split in splits:
            click.echo(f"{split['member']}: ${split['amount']:.2f}")
    else:
        click.echo("No bills to process.")


@cli.command()
@click.option('--months', default=3, help='Number of months to show')
@click.option('--config', default='config/config.yaml', help='Path to configuration file')
def summary(months, config):
    """Show bill summary for the last N months."""
    app = UtilityBillApp(config)
    summary_data = app.get_bill_summary(months)
    
    click.echo(f"\n=== Bill Summary (Last {months} months) ===")
    for bill in summary_data:
        click.echo(f"{bill['date']} - {bill['type']}: ${bill['amount']:.2f}")


@cli.command()
@click.option('--type', required=True, help='Bill type (electricity/gas)')
@click.option('--amount', required=True, type=float, help='Bill amount')
@click.option('--date', required=True, help='Bill date (YYYY-MM-DD)')
@click.option('--config', default='config/config.yaml', help='Path to configuration file')
def add_bill(type, amount, date, config):
    """Manually add a bill."""
    app = UtilityBillApp(config)
    app.manual_add_bill(type, amount, date)
    click.echo(f"Added {type} bill: ${amount} for {date}")


@cli.command()
def setup():
    """Setup the application (create config file, database, etc.)."""
    click.echo("Setting up Utility Bill Calculator...")
    
    # Create necessary directories
    os.makedirs('config', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # Check if config file exists
    if not os.path.exists('config/config.yaml'):
        click.echo("Creating configuration file from template...")
        # Copy template to config.yaml
        import shutil
        shutil.copy('config/config_template.yaml', 'config/config.yaml')
        click.echo("✓ Configuration file created at config/config.yaml")
        click.echo("Please edit this file with your settings before running the application.")
    else:
        click.echo("✓ Configuration file already exists")
    
    click.echo("✓ Setup complete!")


if __name__ == '__main__':
    cli()
