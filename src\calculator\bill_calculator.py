"""
Bill Calculator for Utility Bill Calculator
Calculates bill splits among household members
"""

from typing import List, Dict, Any
from datetime import datetime
from loguru import logger


class BillCalculator:
    """Calculates utility bill splits among household members."""
    
    def __init__(self, household_config: Dict[str, Any]):
        """Initialize bill calculator with household configuration."""
        self.household_config = household_config
        self.total_members = household_config['total_members']
        self.members = household_config['members']
        
        logger.info(f"Bill calculator initialized for {self.total_members} members")
    
    def calculate_splits(self, bills: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Calculate bill splits for a list of bills.
        
        Args:
            bills: List of bill dictionaries
            
        Returns:
            List of split calculations
        """
        all_splits = []
        
        for bill in bills:
            splits = self.calculate_single_bill_split(bill)
            if splits:
                all_splits.extend(splits)
        
        # Group splits by member and bill
        grouped_splits = self._group_splits_by_member(all_splits)
        
        logger.info(f"Calculated splits for {len(bills)} bills across {len(grouped_splits)} members")
        return grouped_splits
    
    def calculate_single_bill_split(self, bill: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Calculate split for a single bill.
        
        Args:
            bill: Bill dictionary
            
        Returns:
            List of split dictionaries for each member
        """
        try:
            bill_amount = float(bill['amount'])
            bill_type = bill['bill_type']
            bill_id = bill.get('id')
            
            # Calculate equal split
            split_amount = bill_amount / self.total_members
            
            splits = []
            for member in self.members:
                split = {
                    'bill_id': bill_id,
                    'bill_type': bill_type,
                    'bill_amount': bill_amount,
                    'bill_date': bill.get('email_date'),
                    'due_date': bill.get('due_date'),
                    'member_name': member['name'],
                    'member_email': member['email'],
                    'member_phone': member.get('phone'),
                    'split_amount': round(split_amount, 2),
                    'calculation_method': 'equal_split',
                    'calculated_date': datetime.now()
                }
                splits.append(split)
            
            logger.debug(f"Split {bill_type} bill of ${bill_amount} into ${split_amount:.2f} per person")
            return splits
            
        except Exception as e:
            logger.error(f"Error calculating split for bill: {e}")
            return []
    
    def calculate_weighted_split(self, bill: Dict[str, Any], weights: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Calculate weighted split based on member weights.
        
        Args:
            bill: Bill dictionary
            weights: Dictionary mapping member names to weight factors
            
        Returns:
            List of split dictionaries
        """
        try:
            bill_amount = float(bill['amount'])
            bill_type = bill['bill_type']
            bill_id = bill.get('id')
            
            # Calculate total weight
            total_weight = sum(weights.get(member['name'], 1.0) for member in self.members)
            
            splits = []
            for member in self.members:
                member_weight = weights.get(member['name'], 1.0)
                split_amount = (bill_amount * member_weight) / total_weight
                
                split = {
                    'bill_id': bill_id,
                    'bill_type': bill_type,
                    'bill_amount': bill_amount,
                    'bill_date': bill.get('email_date'),
                    'due_date': bill.get('due_date'),
                    'member_name': member['name'],
                    'member_email': member['email'],
                    'member_phone': member.get('phone'),
                    'split_amount': round(split_amount, 2),
                    'weight': member_weight,
                    'calculation_method': 'weighted_split',
                    'calculated_date': datetime.now()
                }
                splits.append(split)
            
            logger.debug(f"Weighted split {bill_type} bill of ${bill_amount} with weights: {weights}")
            return splits
            
        except Exception as e:
            logger.error(f"Error calculating weighted split: {e}")
            return []
    
    def calculate_usage_based_split(self, bill: Dict[str, Any], usage_data: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Calculate split based on actual usage data.
        
        Args:
            bill: Bill dictionary
            usage_data: Dictionary mapping member names to usage amounts
            
        Returns:
            List of split dictionaries
        """
        try:
            bill_amount = float(bill['amount'])
            bill_type = bill['bill_type']
            bill_id = bill.get('id')
            
            # Calculate total usage
            total_usage = sum(usage_data.get(member['name'], 0) for member in self.members)
            
            if total_usage == 0:
                # Fall back to equal split if no usage data
                return self.calculate_single_bill_split(bill)
            
            splits = []
            for member in self.members:
                member_usage = usage_data.get(member['name'], 0)
                split_amount = (bill_amount * member_usage) / total_usage
                
                split = {
                    'bill_id': bill_id,
                    'bill_type': bill_type,
                    'bill_amount': bill_amount,
                    'bill_date': bill.get('email_date'),
                    'due_date': bill.get('due_date'),
                    'member_name': member['name'],
                    'member_email': member['email'],
                    'member_phone': member.get('phone'),
                    'split_amount': round(split_amount, 2),
                    'usage_amount': member_usage,
                    'calculation_method': 'usage_based_split',
                    'calculated_date': datetime.now()
                }
                splits.append(split)
            
            logger.debug(f"Usage-based split {bill_type} bill of ${bill_amount}")
            return splits
            
        except Exception as e:
            logger.error(f"Error calculating usage-based split: {e}")
            return []
    
    def _group_splits_by_member(self, splits: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Group splits by member for easier processing."""
        member_splits = {}
        
        for split in splits:
            member_name = split['member_name']
            if member_name not in member_splits:
                member_splits[member_name] = {
                    'member_name': member_name,
                    'member_email': split['member_email'],
                    'member_phone': split.get('member_phone'),
                    'total_amount': 0,
                    'bills': []
                }
            
            member_splits[member_name]['total_amount'] += split['split_amount']
            member_splits[member_name]['bills'].append({
                'bill_id': split['bill_id'],
                'bill_type': split['bill_type'],
                'bill_amount': split['bill_amount'],
                'split_amount': split['split_amount'],
                'bill_date': split['bill_date'],
                'due_date': split['due_date']
            })
        
        # Round total amounts
        for member_data in member_splits.values():
            member_data['total_amount'] = round(member_data['total_amount'], 2)
        
        return list(member_splits.values())
    
    def get_member_summary(self, member_name: str, splits: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get summary for a specific member."""
        member_splits = [s for s in splits if s['member_name'] == member_name]
        
        if not member_splits:
            return None
        
        total_amount = sum(s['split_amount'] for s in member_splits)
        bill_types = list(set(s['bill_type'] for s in member_splits))
        
        return {
            'member_name': member_name,
            'total_amount': round(total_amount, 2),
            'bill_count': len(member_splits),
            'bill_types': bill_types,
            'splits': member_splits
        }
    
    def calculate_monthly_average(self, historical_splits: List[Dict[str, Any]], months: int = 3) -> Dict[str, float]:
        """Calculate monthly average for each member."""
        member_totals = {}
        
        for split in historical_splits:
            member_name = split['member_name']
            if member_name not in member_totals:
                member_totals[member_name] = 0
            member_totals[member_name] += split['split_amount']
        
        # Calculate averages
        averages = {}
        for member_name, total in member_totals.items():
            averages[member_name] = round(total / months, 2)
        
        return averages
    
    def validate_split_calculation(self, bill: Dict[str, Any], splits: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate that splits add up correctly."""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            original_amount = float(bill['amount'])
            total_split = sum(float(s['split_amount']) for s in splits)
            
            # Check if totals match (within 1 cent tolerance for rounding)
            if abs(original_amount - total_split) > 0.01:
                validation['is_valid'] = False
                validation['errors'].append(
                    f"Split total ${total_split:.2f} doesn't match bill amount ${original_amount:.2f}"
                )
            
            # Check if all members are included
            split_members = set(s['member_name'] for s in splits)
            config_members = set(m['name'] for m in self.members)
            
            if split_members != config_members:
                missing = config_members - split_members
                extra = split_members - config_members
                
                if missing:
                    validation['warnings'].append(f"Missing members in split: {missing}")
                if extra:
                    validation['warnings'].append(f"Extra members in split: {extra}")
            
            # Check for negative amounts
            negative_splits = [s for s in splits if s['split_amount'] < 0]
            if negative_splits:
                validation['errors'].append("Found negative split amounts")
                validation['is_valid'] = False
            
        except Exception as e:
            validation['is_valid'] = False
            validation['errors'].append(f"Validation error: {str(e)}")
        
        return validation
    
    def generate_split_report(self, splits: List[Dict[str, Any]]) -> str:
        """Generate a formatted report of bill splits."""
        if not splits:
            return "No bill splits to report."
        
        report_lines = []
        report_lines.append("=== UTILITY BILL SPLIT REPORT ===")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Group by member
        grouped_splits = self._group_splits_by_member(splits)
        
        for member_data in grouped_splits:
            report_lines.append(f"Member: {member_data['member_name']}")
            report_lines.append(f"Email: {member_data['member_email']}")
            report_lines.append(f"Total Amount: ${member_data['total_amount']:.2f}")
            report_lines.append("Bills:")
            
            for bill in member_data['bills']:
                report_lines.append(f"  - {bill['bill_type'].title()}: ${bill['split_amount']:.2f} (Total: ${bill['bill_amount']:.2f})")
            
            report_lines.append("")
        
        return "\n".join(report_lines)
